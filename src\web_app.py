"""
Web application for XactAnalysis automation.

This module provides a web-based interface for controlling the XactAnalysis
automation system, allowing users to process claims through a simple UI.
"""

import json
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List

from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_cors import CORS

from .utils.config_manager import ConfigManager
from .utils.logger import get_logger
from .data.excel_processor import ExcelProcessor
from .automation.xact_analysis_bot import XactAnalysisBot
from .automation.pdf_generator import PDFGenerator


# Initialize Flask app
app = Flask(__name__, template_folder='../templates', static_folder='../static')
CORS(app)

# Global instances
config = ConfigManager()
logger = get_logger(__name__, config)
excel_processor = ExcelProcessor(config)
xa_bot = None
pdf_generator = PDFGenerator(config)

# Ensure directories exist
config.ensure_directories()


@app.route('/')
def index():
    """Main dashboard page."""
    try:
        # Get claims summary
        claims_summary = get_claims_summary()
        
        # Get system status
        system_status = get_system_status()
        
        return render_template('index.html', 
                             claims_summary=claims_summary,
                             system_status=system_status)
    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        return render_template('error.html', error=str(e))


@app.route('/api/status')
def api_status():
    """Get current system status."""
    try:
        status = get_system_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/claims')
def api_claims():
    """Get claims data."""
    try:
        claims_data = get_claims_data()
        return jsonify(claims_data)
    except Exception as e:
        logger.error(f"Error getting claims: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/connect', methods=['POST'])
def api_connect():
    """Connect to existing browser session."""
    global xa_bot
    
    try:
        data = request.get_json() or {}
        debug_port = data.get('debug_port', 9222)
        
        logger.info(f"Attempting to connect to browser on port {debug_port}")
        
        # Clean up existing connection
        if xa_bot:
            xa_bot.cleanup()
        
        # Create new bot and connect
        xa_bot = XactAnalysisBot(config)
        success = xa_bot.connect_to_session(debug_port)
        
        if success:
            status = xa_bot.get_current_status()
            logger.info("Successfully connected to browser session")
            return jsonify({
                'success': True,
                'message': 'Connected to browser session',
                'status': status
            })
        else:
            logger.error("Failed to connect to browser session")
            return jsonify({
                'success': False,
                'message': 'Failed to connect to browser session'
            }), 400
            
    except Exception as e:
        logger.error(f"Error connecting to browser: {e}")
        return jsonify({
            'success': False,
            'message': f'Connection error: {str(e)}'
        }), 500


@app.route('/api/process_claim', methods=['POST'])
def api_process_claim():
    """Process a single claim."""
    try:
        data = request.get_json()
        claim_number = data.get('claim_number')
        
        if not claim_number:
            return jsonify({'success': False, 'message': 'Claim number required'}), 400
        
        if not xa_bot or not xa_bot.browser_manager.is_connected():
            return jsonify({'success': False, 'message': 'Browser not connected'}), 400
        
        logger.info(f"Processing claim: {claim_number}")
        
        # Process the claim
        result = process_single_claim(claim_number)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing claim: {e}")
        return jsonify({
            'success': False,
            'message': f'Processing error: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


@app.route('/api/process_next', methods=['POST'])
def api_process_next():
    """Process the next pending claim."""
    try:
        if not xa_bot or not xa_bot.browser_manager.is_connected():
            return jsonify({'success': False, 'message': 'Browser not connected'}), 400
        
        # Get next pending claim
        pending_claims = excel_processor.get_pending_claims()
        
        if not pending_claims:
            return jsonify({
                'success': True,
                'message': 'No pending claims to process',
                'completed': True
            })
        
        next_claim = pending_claims[0]
        claim_number = next_claim['claim_number']
        
        logger.info(f"Processing next claim: {claim_number}")
        
        # Process the claim
        result = process_single_claim(claim_number, next_claim['row_index'])
        
        # Add progress information
        result['progress'] = {
            'current': len(pending_claims) - (1 if result['success'] else 0),
            'total': len(excel_processor.load_claims()),
            'remaining': len(pending_claims) - (1 if result['success'] else 0)
        }
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"Error processing next claim: {e}")
        return jsonify({
            'success': False,
            'message': f'Processing error: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500


def process_single_claim(claim_number: str, row_index: int = None) -> Dict[str, Any]:
    """
    Process a single claim through the automation workflow.
    
    Args:
        claim_number: The claim number to process
        row_index: Optional row index for faster Excel updates
        
    Returns:
        Dictionary with processing results
    """
    try:
        start_time = datetime.now()
        
        # Step 1: Search for claim
        logger.info(f"Step 1: Searching for claim {claim_number}")
        search_success = xa_bot.search_claim(claim_number)
        
        if not search_success:
            return {
                'success': False,
                'claim_number': claim_number,
                'message': f'Claim {claim_number} not found',
                'step': 'search'
            }
        
        # Step 2: Navigate to notes
        logger.info(f"Step 2: Navigating to notes for claim {claim_number}")
        notes_success = xa_bot.navigate_to_notes()
        
        if not notes_success:
            return {
                'success': False,
                'claim_number': claim_number,
                'message': f'Could not navigate to notes for claim {claim_number}',
                'step': 'navigation'
            }
        
        # Step 3: Generate PDF(s)
        logger.info(f"Step 3: Generating PDF for claim {claim_number}")
        page = xa_bot.browser_manager.page
        
        # Check for multiple entries
        num_entries = pdf_generator.check_for_multiple_entries(page)
        generated_pdfs = []
        
        for entry_index in range(num_entries):
            if entry_index > 0:
                # Select the specific entry
                pdf_generator.select_entry(page, entry_index)
            
            # Generate PDF
            pdf_path = pdf_generator.generate_pdf_from_page(page, claim_number, entry_index)
            
            if pdf_path:
                generated_pdfs.append(str(pdf_path))
                logger.info(f"Generated PDF: {pdf_path}")
        
        if not generated_pdfs:
            return {
                'success': False,
                'claim_number': claim_number,
                'message': f'Failed to generate PDF for claim {claim_number}',
                'step': 'pdf_generation'
            }
        
        # Step 4: Mark as completed in Excel
        logger.info(f"Step 4: Marking claim {claim_number} as completed")
        excel_success = excel_processor.mark_claim_completed(claim_number, row_index)
        
        if not excel_success:
            logger.warning(f"Failed to mark claim {claim_number} as completed in Excel")
        
        # Calculate processing time
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        return {
            'success': True,
            'claim_number': claim_number,
            'message': f'Successfully processed claim {claim_number}',
            'generated_pdfs': generated_pdfs,
            'num_entries': num_entries,
            'processing_time': processing_time,
            'excel_updated': excel_success
        }
        
    except Exception as e:
        logger.error(f"Error processing claim {claim_number}: {e}")
        return {
            'success': False,
            'claim_number': claim_number,
            'message': f'Processing failed: {str(e)}',
            'error': str(e),
            'traceback': traceback.format_exc()
        }


def get_system_status() -> Dict[str, Any]:
    """Get current system status."""
    try:
        browser_connected = xa_bot and xa_bot.browser_manager.is_connected()
        
        status = {
            'browser_connected': browser_connected,
            'current_url': xa_bot.browser_manager.get_current_url() if browser_connected else None,
            'page_title': xa_bot.browser_manager.get_page_title() if browser_connected else None,
            'output_dir': str(config.output_dir),
            'excel_file': str(config.excel_file_path),
            'timestamp': datetime.now().isoformat()
        }
        
        return status
        
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        return {'error': str(e)}


def get_claims_summary() -> Dict[str, Any]:
    """Get claims summary data."""
    try:
        all_claims = excel_processor.load_claims()
        pending_claims = excel_processor.get_pending_claims()
        
        completed_count = len(all_claims) - len(pending_claims)
        
        return {
            'total': len(all_claims),
            'completed': completed_count,
            'pending': len(pending_claims),
            'progress_percent': round((completed_count / len(all_claims)) * 100, 1) if all_claims else 0
        }
        
    except Exception as e:
        logger.error(f"Error getting claims summary: {e}")
        return {'error': str(e)}


def get_claims_data() -> Dict[str, Any]:
    """Get detailed claims data."""
    try:
        all_claims = excel_processor.load_claims()
        pending_claims = excel_processor.get_pending_claims()
        
        # Get recent claims (last 10)
        recent_claims = all_claims[-10:] if len(all_claims) > 10 else all_claims
        
        return {
            'summary': get_claims_summary(),
            'recent_claims': recent_claims,
            'next_pending': pending_claims[:5] if pending_claims else []
        }
        
    except Exception as e:
        logger.error(f"Error getting claims data: {e}")
        return {'error': str(e)}


if __name__ == '__main__':
    logger.info("Starting XactAnalysis automation web application")
    app.run(debug=True, host='127.0.0.1', port=5000)
