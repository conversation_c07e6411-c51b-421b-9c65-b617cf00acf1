// XactAnalysis Automation Dashboard JavaScript

class Dashboard {
    constructor() {
        this.isConnected = false;
        this.isProcessing = false;
        this.autoProcessing = false;
        
        this.initializeElements();
        this.bindEvents();
        this.updateTime();
        this.refreshStatus();
        this.refreshFileInfo();

        // Update time every second
        setInterval(() => this.updateTime(), 1000);

        // Refresh status every 30 seconds
        setInterval(() => this.refreshStatus(), 30000);
    }
    
    initializeElements() {
        // Connection elements
        this.connectBtn = document.getElementById('connect-btn');
        this.debugPortInput = document.getElementById('debug-port');
        this.connectionStatus = document.getElementById('connection-status');
        this.connectionInfo = document.getElementById('connection-info');
        this.currentUrl = document.getElementById('current-url');
        this.pageTitle = document.getElementById('page-title');
        
        // CSV upload elements
        this.csvUpload = document.getElementById('csv-upload');
        this.uploadCsvBtn = document.getElementById('upload-csv-btn');
        this.currentFileInfo = document.getElementById('current-file-info');

        // Processing elements
        this.claimNumberInput = document.getElementById('claim-number');
        this.processClaimBtn = document.getElementById('process-claim-btn');
        this.processNextBtn = document.getElementById('process-next-btn');
        this.autoProcessBtn = document.getElementById('auto-process-btn');
        
        // Status elements
        this.totalClaims = document.getElementById('total-claims');
        this.completedClaims = document.getElementById('completed-claims');
        this.pendingClaims = document.getElementById('pending-claims');
        this.progressPercent = document.getElementById('progress-percent');
        this.progressBar = document.getElementById('progress-bar');
        
        // Activity log
        this.activityLog = document.getElementById('activity-log');
        this.clearLogBtn = document.getElementById('clear-log-btn');
        
        // Current processing
        this.currentProcessing = document.getElementById('current-processing');
        this.currentClaim = document.getElementById('current-claim');
        this.currentStep = document.getElementById('current-step');
        
        // Time
        this.currentTime = document.getElementById('current-time');
    }
    
    bindEvents() {
        // Connection
        this.connectBtn.addEventListener('click', () => this.connectToBrowser());

        // CSV Upload
        this.csvUpload.addEventListener('change', () => this.onCsvFileSelected());
        this.uploadCsvBtn.addEventListener('click', () => this.uploadCsvFile());

        // Processing
        this.processClaimBtn.addEventListener('click', () => this.processSpecificClaim());
        this.processNextBtn.addEventListener('click', () => this.processNextClaim());
        this.autoProcessBtn.addEventListener('click', () => this.toggleAutoProcessing());
        
        // Claim number input
        this.claimNumberInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.processSpecificClaim();
            }
        });
        
        // Clear log
        this.clearLogBtn.addEventListener('click', () => this.clearActivityLog());
    }
    
    updateTime() {
        const now = new Date();
        this.currentTime.textContent = now.toLocaleTimeString();
    }
    
    async refreshStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            this.updateConnectionStatus(status.browser_connected);
            
            if (status.browser_connected) {
                this.currentUrl.textContent = status.current_url || '-';
                this.pageTitle.textContent = status.page_title || '-';
                this.connectionInfo.style.display = 'block';
            } else {
                this.connectionInfo.style.display = 'none';
            }
            
            // Refresh claims data
            await this.refreshClaimsData();
            
        } catch (error) {
            console.error('Error refreshing status:', error);
            this.addLogEntry('Error refreshing status', 'error');
        }
    }
    
    async refreshClaimsData() {
        try {
            const response = await fetch('/api/claims');
            const data = await response.json();
            
            if (data.summary) {
                this.updateProgressDisplay(data.summary);
            }
            
        } catch (error) {
            console.error('Error refreshing claims data:', error);
        }
    }
    
    updateConnectionStatus(connected) {
        this.isConnected = connected;
        
        if (connected) {
            this.connectionStatus.className = 'badge bg-success me-2 connected';
            this.connectionStatus.innerHTML = '<i class="fas fa-circle"></i> Connected';
            this.connectBtn.textContent = 'Connected';
            this.connectBtn.className = 'btn btn-success w-100';
            this.connectBtn.disabled = true;
        } else {
            this.connectionStatus.className = 'badge bg-secondary me-2 disconnected';
            this.connectionStatus.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
            this.connectBtn.textContent = 'Connect to Browser';
            this.connectBtn.className = 'btn btn-primary w-100';
            this.connectBtn.disabled = false;
        }
        
        // Enable/disable processing buttons
        this.processClaimBtn.disabled = !connected || this.isProcessing;
        this.processNextBtn.disabled = !connected || this.isProcessing;
        this.autoProcessBtn.disabled = !connected || this.isProcessing;
    }
    
    updateProgressDisplay(summary) {
        this.totalClaims.textContent = summary.total || 0;
        this.completedClaims.textContent = summary.completed || 0;
        this.pendingClaims.textContent = summary.pending || 0;
        this.progressPercent.textContent = `${summary.progress_percent || 0}%`;
        this.progressBar.style.width = `${summary.progress_percent || 0}%`;
    }
    
    async connectToBrowser() {
        const debugPort = this.debugPortInput.value || 9222;
        
        this.setButtonLoading(this.connectBtn, true);
        this.addLogEntry(`Attempting to connect to browser on port ${debugPort}...`, 'info');
        
        try {
            const response = await fetch('/api/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ debug_port: parseInt(debugPort) })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.addLogEntry('Successfully connected to browser!', 'success');
                this.updateConnectionStatus(true);
                
                if (result.status) {
                    this.currentUrl.textContent = result.status.current_url || '-';
                    this.pageTitle.textContent = result.status.page_title || '-';
                    this.connectionInfo.style.display = 'block';
                }
            } else {
                this.addLogEntry(`Connection failed: ${result.message}`, 'error');
                this.updateConnectionStatus(false);
            }
            
        } catch (error) {
            console.error('Connection error:', error);
            this.addLogEntry(`Connection error: ${error.message}`, 'error');
            this.updateConnectionStatus(false);
        } finally {
            this.setButtonLoading(this.connectBtn, false);
        }
    }
    
    async processSpecificClaim() {
        const claimNumber = this.claimNumberInput.value.trim();
        
        if (!claimNumber) {
            this.addLogEntry('Please enter a claim number', 'warning');
            return;
        }
        
        await this.processClaim(claimNumber);
        this.claimNumberInput.value = '';
    }
    
    async processNextClaim() {
        await this.processClaim();
    }
    
    async processClaim(specificClaim = null) {
        if (this.isProcessing) {
            this.addLogEntry('Already processing a claim', 'warning');
            return;
        }
        
        this.setProcessingState(true);
        
        const endpoint = specificClaim ? '/api/process_claim' : '/api/process_next';
        const payload = specificClaim ? { claim_number: specificClaim } : {};
        
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            
            if (result.success) {
                const claimNum = result.claim_number;
                const processingTime = result.processing_time ? ` (${result.processing_time.toFixed(1)}s)` : '';
                
                this.addLogEntry(`✅ Successfully processed claim ${claimNum}${processingTime}`, 'success');
                
                if (result.generated_pdfs && result.generated_pdfs.length > 0) {
                    this.addLogEntry(`📄 Generated ${result.generated_pdfs.length} PDF(s)`, 'info');
                }
                
                // Update progress
                await this.refreshClaimsData();
                
                // Continue auto processing if enabled
                if (this.autoProcessing && result.progress && result.progress.remaining > 0) {
                    setTimeout(() => this.processNextClaim(), 2000);
                } else if (this.autoProcessing) {
                    this.toggleAutoProcessing(); // Stop auto processing
                    this.addLogEntry('🎉 All claims processed!', 'success');
                }
                
            } else {
                this.addLogEntry(`❌ Failed to process claim: ${result.message}`, 'error');
                
                if (this.autoProcessing) {
                    this.toggleAutoProcessing(); // Stop auto processing on error
                }
            }
            
        } catch (error) {
            console.error('Processing error:', error);
            this.addLogEntry(`❌ Processing error: ${error.message}`, 'error');
            
            if (this.autoProcessing) {
                this.toggleAutoProcessing(); // Stop auto processing on error
            }
        } finally {
            this.setProcessingState(false);
        }
    }
    
    toggleAutoProcessing() {
        this.autoProcessing = !this.autoProcessing;
        
        if (this.autoProcessing) {
            this.autoProcessBtn.innerHTML = '<i class="fas fa-stop me-2"></i>Stop Auto Process';
            this.autoProcessBtn.className = 'btn btn-danger';
            this.addLogEntry('🚀 Starting auto processing...', 'info');
            this.processNextClaim();
        } else {
            this.autoProcessBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Auto Process All';
            this.autoProcessBtn.className = 'btn btn-warning';
            this.addLogEntry('⏸️ Auto processing stopped', 'warning');
        }
    }
    
    setProcessingState(processing) {
        this.isProcessing = processing;
        
        // Update button states
        this.processClaimBtn.disabled = !this.isConnected || processing;
        this.processNextBtn.disabled = !this.isConnected || processing;
        
        if (!this.autoProcessing) {
            this.autoProcessBtn.disabled = !this.isConnected || processing;
        }
        
        // Show/hide processing indicator
        if (processing) {
            this.currentProcessing.style.display = 'block';
        } else {
            this.currentProcessing.style.display = 'none';
        }
    }
    
    setButtonLoading(button, loading) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }
    
    addLogEntry(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const entry = document.createElement('div');
        entry.className = `log-entry ${type}`;
        
        const icon = this.getLogIcon(type);
        entry.innerHTML = `<small><i class="${icon} me-1"></i>[${timestamp}] ${message}</small>`;
        
        this.activityLog.appendChild(entry);
        this.activityLog.scrollTop = this.activityLog.scrollHeight;
        
        // Limit log entries to prevent memory issues
        const entries = this.activityLog.children;
        if (entries.length > 100) {
            this.activityLog.removeChild(entries[0]);
        }
    }
    
    getLogIcon(type) {
        switch (type) {
            case 'success': return 'fas fa-check-circle';
            case 'error': return 'fas fa-exclamation-circle';
            case 'warning': return 'fas fa-exclamation-triangle';
            case 'info': return 'fas fa-info-circle';
            default: return 'fas fa-circle';
        }
    }
    
    clearActivityLog() {
        this.activityLog.innerHTML = '<div class="log-entry text-muted"><small><i class="fas fa-info-circle me-1"></i>Activity log cleared</small></div>';
    }

    async refreshFileInfo() {
        try {
            const response = await fetch('/api/current_file_info');
            const data = await response.json();

            if (data.success && data.file_info) {
                const info = data.file_info;
                const fileName = info.file_path.split(/[/\\]/).pop();
                const fileSize = (info.file_size / 1024).toFixed(1);

                this.currentFileInfo.innerHTML = `
                    <strong>File:</strong> ${fileName}<br>
                    <strong>Size:</strong> ${fileSize} KB<br>
                    <strong>Rows:</strong> ${info.total_rows}<br>
                    <strong>Columns:</strong> ${info.total_columns}
                `;
            } else {
                this.currentFileInfo.innerHTML = '<span class="text-danger">Error loading file info</span>';
            }

        } catch (error) {
            console.error('Error refreshing file info:', error);
            this.currentFileInfo.innerHTML = '<span class="text-danger">Error loading file info</span>';
        }
    }

    onCsvFileSelected() {
        const file = this.csvUpload.files[0];

        if (file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.csv')) {
                this.addLogEntry('Please select a CSV file', 'warning');
                this.csvUpload.value = '';
                this.uploadCsvBtn.disabled = true;
                return;
            }

            // Validate file size (max 16MB)
            if (file.size > 16 * 1024 * 1024) {
                this.addLogEntry('File too large. Maximum size is 16MB', 'warning');
                this.csvUpload.value = '';
                this.uploadCsvBtn.disabled = true;
                return;
            }

            this.uploadCsvBtn.disabled = false;
            this.addLogEntry(`Selected file: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`, 'info');
        } else {
            this.uploadCsvBtn.disabled = true;
        }
    }

    async uploadCsvFile() {
        const file = this.csvUpload.files[0];

        if (!file) {
            this.addLogEntry('Please select a CSV file first', 'warning');
            return;
        }

        this.setButtonLoading(this.uploadCsvBtn, true);
        this.addLogEntry(`Uploading ${file.name}...`, 'info');

        try {
            const formData = new FormData();
            formData.append('csv_file', file);

            const response = await fetch('/api/upload_csv', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.addLogEntry(`✅ Successfully uploaded and switched to ${file.name}`, 'success');

                // Update file info
                await this.refreshFileInfo();

                // Update claims data
                if (result.claims_summary) {
                    this.updateProgressDisplay(result.claims_summary);
                }

                // Clear the file input
                this.csvUpload.value = '';
                this.uploadCsvBtn.disabled = true;

                // Refresh the page data
                await this.refreshClaimsData();

            } else {
                this.addLogEntry(`❌ Upload failed: ${result.message}`, 'error');
            }

        } catch (error) {
            console.error('Upload error:', error);
            this.addLogEntry(`❌ Upload error: ${error.message}`, 'error');
        } finally {
            this.setButtonLoading(this.uploadCsvBtn, false);
        }
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});
