"""
Browser management for XactAnalysis automation.

This module handles browser connections, session management, and provides
a foundation for interacting with XactAnalysis through an existing session.
"""

import time
from typing import Optional, List, Dict, Any
from pathlib import Path
from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import BrowserError, NavigationError, TimeoutError


class BrowserManager:
    """Manages browser connections and sessions for XactAnalysis automation."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the browser manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.browser_type = self.config.get('browser', 'browser_type', 'chromium')
        self.headless = self.config.get_bool('browser', 'headless', False)
        self.timeout = self.config.get_int('browser', 'timeout', 30000)
        self.page_timeout = self.config.get_int('browser', 'page_timeout', 60000)
        
        # Browser instances
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        self.logger.info("Browser manager initialized")
    
    def connect_to_existing_session(self, debug_port: int = 9222) -> bool:
        """
        Connect to an existing browser session.
        
        Args:
            debug_port: Chrome debug port (default 9222)
            
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.logger.info(f"Attempting to connect to existing browser session on port {debug_port}")
            
            self.playwright = sync_playwright().start()
            
            # Connect to existing browser
            self.browser = self.playwright.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            
            # Get existing contexts
            contexts = self.browser.contexts
            if not contexts:
                self.logger.warning("No existing browser contexts found")
                return False
            
            # Use the first context
            self.context = contexts[0]
            
            # Get existing pages
            pages = self.context.pages
            if not pages:
                self.logger.warning("No existing pages found")
                return False
            
            # Find XactAnalysis page or use the first page
            xa_page = None
            for page in pages:
                try:
                    url = page.url
                    if 'xactanalysis' in url.lower():
                        xa_page = page
                        break
                except:
                    continue
            
            self.page = xa_page or pages[0]
            
            # Set timeouts
            self.page.set_default_timeout(self.timeout)
            self.page.set_default_navigation_timeout(self.page_timeout)
            
            self.logger.info(f"Successfully connected to existing session. Current URL: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to existing session: {e}")
            self.cleanup()
            return False
    
    def launch_new_session(self) -> bool:
        """
        Launch a new browser session.
        
        Returns:
            True if launch successful, False otherwise
        """
        try:
            self.logger.info("Launching new browser session")
            
            self.playwright = sync_playwright().start()
            
            # Browser launch options
            launch_options = {
                'headless': self.headless,
                'args': [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            }
            
            # Launch browser
            if self.browser_type == 'chromium':
                self.browser = self.playwright.chromium.launch(**launch_options)
            elif self.browser_type == 'firefox':
                self.browser = self.playwright.firefox.launch(**launch_options)
            elif self.browser_type == 'webkit':
                self.browser = self.playwright.webkit.launch(**launch_options)
            else:
                raise BrowserError(f"Unsupported browser type: {self.browser_type}")
            
            # Create context
            self.context = self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            
            # Create page
            self.page = self.context.new_page()
            
            # Set timeouts
            self.page.set_default_timeout(self.timeout)
            self.page.set_default_navigation_timeout(self.page_timeout)
            
            self.logger.info("Successfully launched new browser session")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to launch new session: {e}")
            self.cleanup()
            return False
    
    def navigate_to_xactanalysis(self) -> bool:
        """
        Navigate to XactAnalysis website.
        
        Returns:
            True if navigation successful, False otherwise
        """
        try:
            if not self.page:
                raise BrowserError("No active page available")
            
            base_url = self.config.get('xactanalysis', 'base_url', 'https://www.xactanalysis.com')
            
            self.logger.info(f"Navigating to XactAnalysis: {base_url}")
            self.page.goto(base_url)
            
            # Wait for page to load
            self.page.wait_for_load_state('networkidle')
            
            self.logger.info("Successfully navigated to XactAnalysis")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to XactAnalysis: {e}")
            return False
    
    def get_current_url(self) -> Optional[str]:
        """Get the current page URL."""
        try:
            return self.page.url if self.page else None
        except:
            return None
    
    def get_page_title(self) -> Optional[str]:
        """Get the current page title."""
        try:
            return self.page.title() if self.page else None
        except:
            return None
    
    def take_screenshot(self, filename: Optional[str] = None) -> Optional[Path]:
        """
        Take a screenshot of the current page.
        
        Args:
            filename: Optional filename for the screenshot
            
        Returns:
            Path to the screenshot file if successful, None otherwise
        """
        try:
            if not self.page:
                return None
            
            screenshot_dir = self.config.get_path('paths', 'screenshot_dir', Path('screenshots'))
            screenshot_dir.mkdir(parents=True, exist_ok=True)
            
            if not filename:
                timestamp = int(time.time())
                filename = f"screenshot_{timestamp}.png"
            
            screenshot_path = screenshot_dir / filename
            self.page.screenshot(path=str(screenshot_path))
            
            self.logger.debug(f"Screenshot saved: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"Failed to take screenshot: {e}")
            return None
    
    def wait_for_element(self, selector: str, timeout: Optional[int] = None) -> bool:
        """
        Wait for an element to appear on the page.
        
        Args:
            selector: CSS selector for the element
            timeout: Optional timeout in milliseconds
            
        Returns:
            True if element found, False otherwise
        """
        try:
            if not self.page:
                return False
            
            wait_timeout = timeout or self.timeout
            self.page.wait_for_selector(selector, timeout=wait_timeout)
            return True
            
        except Exception as e:
            self.logger.warning(f"Element not found: {selector} - {e}")
            return False
    
    def is_connected(self) -> bool:
        """Check if browser is connected and page is available."""
        try:
            return (self.browser is not None and 
                   self.page is not None and 
                   not self.browser.is_connected() is False)
        except:
            return False
    
    def cleanup(self) -> None:
        """Clean up browser resources."""
        try:
            if self.page:
                self.page.close()
                self.page = None
            
            if self.context:
                self.context.close()
                self.context = None
            
            if self.browser:
                self.browser.close()
                self.browser = None
            
            if self.playwright:
                self.playwright.stop()
                self.playwright = None
            
            self.logger.info("Browser resources cleaned up")
            
        except Exception as e:
            self.logger.warning(f"Error during cleanup: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
