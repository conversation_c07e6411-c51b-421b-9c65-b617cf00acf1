"""
XactAnalysis automation bot for claim note extraction.

This module provides automation capabilities for interacting with XactAnalysis
to search for claims, navigate to notes, and extract PDF documents.
"""

import time
from typing import Optional, List, Dict, Any, Tuple
from pathlib import Path

from .browser_manager import BrowserManager
from ..utils.config_manager import ConfigManager
from ..utils.logger import get_logger
from ..utils.exceptions import (
    ClaimNotFoundException, NavigationError, PDFGenerationError, 
    BrowserError, TimeoutError
)


class XactAnalysisBot:
    """Automation bot for XactAnalysis claim note extraction."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """
        Initialize the XactAnalysis bot.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config = config_manager or ConfigManager()
        self.logger = get_logger(__name__, self.config)
        
        # Configuration
        self.search_delay = self.config.get_float('xactanalysis', 'search_delay', 2.0)
        self.nav_delay = self.config.get_float('xactanalysis', 'nav_delay', 1.0)
        self.output_dir = self.config.output_dir
        
        # Browser manager
        self.browser_manager = BrowserManager(self.config)
        
        # Ensure output directory exists
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("XactAnalysis bot initialized")
    
    def connect_to_session(self, debug_port: int = 9222) -> bool:
        """
        Connect to an existing browser session.
        
        Args:
            debug_port: Chrome debug port
            
        Returns:
            True if connection successful
        """
        return self.browser_manager.connect_to_existing_session(debug_port)
    
    def search_claim(self, claim_number: str) -> bool:
        """
        Search for a specific claim number in XactAnalysis.
        
        Args:
            claim_number: The claim number to search for
            
        Returns:
            True if claim found and selected, False otherwise
            
        Raises:
            ClaimNotFoundException: If claim is not found
            NavigationError: If navigation fails
        """
        try:
            self.logger.info(f"Searching for claim: {claim_number}")
            
            if not self.browser_manager.is_connected():
                raise BrowserError("Browser not connected")
            
            page = self.browser_manager.page
            
            # Take screenshot before search
            self.browser_manager.take_screenshot(f"before_search_{claim_number}.png")
            
            # Common search selectors (these may need adjustment based on actual XA interface)
            search_selectors = [
                'input[placeholder*="search" i]',
                'input[placeholder*="claim" i]',
                'input[name*="search" i]',
                'input[id*="search" i]',
                '#search',
                '.search-input',
                '[data-testid*="search"]'
            ]
            
            # Try to find search input
            search_input = None
            for selector in search_selectors:
                try:
                    if page.query_selector(selector):
                        search_input = selector
                        break
                except:
                    continue
            
            if not search_input:
                # Try to find any input field that might be for search
                inputs = page.query_selector_all('input[type="text"], input:not([type])')
                if inputs:
                    search_input = 'input[type="text"], input:not([type])'
                else:
                    raise NavigationError("Could not find search input field")
            
            self.logger.debug(f"Using search selector: {search_input}")
            
            # Clear and enter claim number
            page.fill(search_input, "")
            time.sleep(0.5)
            page.fill(search_input, claim_number)
            
            # Wait a moment for any autocomplete
            time.sleep(self.search_delay)
            
            # Try different ways to submit search
            search_submitted = False
            
            # Method 1: Press Enter
            try:
                page.press(search_input, 'Enter')
                search_submitted = True
                self.logger.debug("Search submitted with Enter key")
            except:
                pass
            
            # Method 2: Look for search button
            if not search_submitted:
                search_buttons = [
                    'button[type="submit"]',
                    'button:has-text("Search")',
                    'button:has-text("Find")',
                    'input[type="submit"]',
                    '.search-button',
                    '[data-testid*="search"]'
                ]
                
                for button_selector in search_buttons:
                    try:
                        if page.query_selector(button_selector):
                            page.click(button_selector)
                            search_submitted = True
                            self.logger.debug(f"Search submitted with button: {button_selector}")
                            break
                    except:
                        continue
            
            if not search_submitted:
                self.logger.warning("Could not submit search - trying Enter key as fallback")
                page.press(search_input, 'Enter')
            
            # Wait for search results
            time.sleep(self.search_delay)
            
            # Take screenshot after search
            self.browser_manager.take_screenshot(f"after_search_{claim_number}.png")
            
            # Look for the claim in results
            claim_found = self._find_and_select_claim(claim_number)
            
            if not claim_found:
                raise ClaimNotFoundException(f"Claim {claim_number} not found in search results")
            
            self.logger.info(f"Successfully found and selected claim: {claim_number}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to search for claim {claim_number}: {e}")
            self.browser_manager.take_screenshot(f"error_search_{claim_number}.png")
            if isinstance(e, (ClaimNotFoundException, NavigationError)):
                raise
            raise NavigationError(f"Search failed for claim {claim_number}: {e}")
    
    def _find_and_select_claim(self, claim_number: str) -> bool:
        """
        Find and select a claim from search results.
        
        Args:
            claim_number: The claim number to find
            
        Returns:
            True if claim found and selected
        """
        try:
            page = self.browser_manager.page
            
            # Look for claim number in various elements
            claim_selectors = [
                f'text="{claim_number}"',
                f'[title*="{claim_number}"]',
                f'[data-claim*="{claim_number}"]',
                f'td:has-text("{claim_number}")',
                f'div:has-text("{claim_number}")',
                f'span:has-text("{claim_number}")',
                f'a:has-text("{claim_number}")'
            ]
            
            for selector in claim_selectors:
                try:
                    element = page.query_selector(selector)
                    if element:
                        # Try to click the element or its parent row
                        clickable_element = element
                        
                        # If it's in a table, try to click the row
                        parent_row = element.query_selector('xpath=ancestor::tr[1]')
                        if parent_row:
                            clickable_element = parent_row
                        
                        clickable_element.click()
                        time.sleep(self.nav_delay)
                        
                        self.logger.debug(f"Clicked claim element with selector: {selector}")
                        return True
                        
                except Exception as e:
                    self.logger.debug(f"Failed to click with selector {selector}: {e}")
                    continue
            
            # If direct selectors don't work, try to find any clickable element containing the claim number
            try:
                page.click(f'text="{claim_number}"')
                time.sleep(self.nav_delay)
                return True
            except:
                pass
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error finding claim {claim_number}: {e}")
            return False
    
    def navigate_to_notes(self) -> bool:
        """
        Navigate to the Notes tab/section.
        
        Returns:
            True if navigation successful
            
        Raises:
            NavigationError: If navigation fails
        """
        try:
            self.logger.info("Navigating to Notes section")
            
            page = self.browser_manager.page
            
            # Take screenshot before navigation
            self.browser_manager.take_screenshot("before_notes_nav.png")
            
            # Common selectors for Notes tab/link
            notes_selectors = [
                'text="Notes"',
                'a:has-text("Notes")',
                'button:has-text("Notes")',
                'tab:has-text("Notes")',
                '[data-tab="notes"]',
                '[data-testid*="notes"]',
                '.notes-tab',
                '#notes-tab',
                'li:has-text("Notes")',
                '[title="Notes"]'
            ]
            
            notes_clicked = False
            for selector in notes_selectors:
                try:
                    if page.query_selector(selector):
                        page.click(selector)
                        notes_clicked = True
                        self.logger.debug(f"Clicked Notes with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.debug(f"Failed to click Notes with selector {selector}: {e}")
                    continue
            
            if not notes_clicked:
                raise NavigationError("Could not find Notes tab/link")
            
            # Wait for notes section to load
            time.sleep(self.nav_delay)
            
            # Take screenshot after navigation
            self.browser_manager.take_screenshot("after_notes_nav.png")
            
            self.logger.info("Successfully navigated to Notes section")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to navigate to Notes: {e}")
            self.browser_manager.take_screenshot("error_notes_nav.png")
            if isinstance(e, NavigationError):
                raise
            raise NavigationError(f"Notes navigation failed: {e}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """
        Get current status information.
        
        Returns:
            Dictionary with current status
        """
        return {
            'connected': self.browser_manager.is_connected(),
            'current_url': self.browser_manager.get_current_url(),
            'page_title': self.browser_manager.get_page_title(),
            'output_dir': str(self.output_dir)
        }
    
    def cleanup(self) -> None:
        """Clean up resources."""
        self.browser_manager.cleanup()
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
