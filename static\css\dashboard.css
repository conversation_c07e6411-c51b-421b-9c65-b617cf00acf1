/* XactAnalysis Automation Dashboard Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: 600;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.stat-card {
    padding: 1rem;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
    margin-bottom: 1rem;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-card p {
    margin-bottom: 0;
    font-size: 0.875rem;
}

.activity-log {
    max-height: 300px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

.log-entry {
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 3px solid #dee2e6;
}

.log-entry.success {
    background-color: #d1e7dd;
    border-left-color: #198754;
    color: #0f5132;
}

.log-entry.error {
    background-color: #f8d7da;
    border-left-color: #dc3545;
    color: #721c24;
}

.log-entry.warning {
    background-color: #fff3cd;
    border-left-color: #ffc107;
    color: #664d03;
}

.log-entry.info {
    background-color: #cff4fc;
    border-left-color: #0dcaf0;
    color: #055160;
}

.progress {
    height: 1rem;
    border-radius: 0.5rem;
}

.progress-bar {
    border-radius: 0.5rem;
}

#connection-status {
    font-size: 0.875rem;
}

#connection-status.connected {
    background-color: #198754 !important;
}

#connection-status.disconnected {
    background-color: #6c757d !important;
}

.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn:disabled {
    opacity: 0.6;
}

.form-control {
    border-radius: 0.375rem;
}

.input-group .btn {
    border-radius: 0 0.375rem 0.375rem 0;
}

.input-group .form-control {
    border-radius: 0.375rem 0 0 0.375rem;
}

code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875em;
}

.form-text {
    font-size: 0.8rem;
}

/* Animation for processing indicator */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.processing {
    animation: pulse 2s infinite;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    .activity-log {
        max-height: 200px;
    }
}

/* Custom scrollbar for activity log */
.activity-log::-webkit-scrollbar {
    width: 6px;
}

.activity-log::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.activity-log::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.activity-log::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background-color: #198754;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.processing {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

/* Card hover effects */
.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transition: box-shadow 0.15s ease-in-out;
}

/* Button loading state */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1050;
}

/* Success/Error message styling */
.alert {
    border-radius: 0.5rem;
    border: none;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-info {
    background-color: #cff4fc;
    color: #055160;
}
