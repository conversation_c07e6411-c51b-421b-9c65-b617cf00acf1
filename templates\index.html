<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XactAnalysis Automation Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dashboard.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <nav class="navbar navbar-dark bg-primary">
                    <div class="container-fluid">
                        <span class="navbar-brand mb-0 h1">
                            <i class="fas fa-file-pdf me-2"></i>
                            XactAnalysis Automation Dashboard
                        </span>
                        <div class="d-flex">
                            <span id="connection-status" class="badge bg-secondary me-2">
                                <i class="fas fa-circle"></i> Disconnected
                            </span>
                            <span id="current-time" class="text-light"></span>
                        </div>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row mt-4">
            <!-- Left Panel - Controls -->
            <div class="col-md-4">
                <!-- Connection Panel -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plug me-2"></i>Browser Connection
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="debug-port" class="form-label">Chrome Debug Port:</label>
                            <input type="number" class="form-control" id="debug-port" value="9222">
                            <div class="form-text">
                                Start Chrome with: <code>--remote-debugging-port=9222</code>
                            </div>
                        </div>
                        <button id="connect-btn" class="btn btn-primary w-100">
                            <i class="fas fa-link me-2"></i>Connect to Browser
                        </button>
                        <div id="connection-info" class="mt-3" style="display: none;">
                            <small class="text-muted">
                                <strong>Current URL:</strong> <span id="current-url">-</span><br>
                                <strong>Page Title:</strong> <span id="page-title">-</span>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- CSV File Management -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-csv me-2"></i>CSV File Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Current File:</label>
                            <div id="current-file-info" class="small text-muted">
                                Loading file information...
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="csv-upload" class="form-label">Upload New CSV:</label>
                            <input type="file" class="form-control" id="csv-upload" accept=".csv">
                            <div class="form-text">
                                CSV should have status in column A and claim numbers in column B
                            </div>
                        </div>
                        <button id="upload-csv-btn" class="btn btn-outline-info w-100" disabled>
                            <i class="fas fa-upload me-2"></i>Upload & Switch to New CSV
                        </button>
                    </div>
                </div>

                <!-- Processing Controls -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-play me-2"></i>Processing Controls
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="claim-number" class="form-label">Process Specific Claim:</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="claim-number" placeholder="Enter claim number">
                                <button id="process-claim-btn" class="btn btn-outline-primary" disabled>
                                    <i class="fas fa-search me-1"></i>Process
                                </button>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button id="process-next-btn" class="btn btn-success" disabled>
                                <i class="fas fa-forward me-2"></i>Process Next Claim
                            </button>
                            <button id="auto-process-btn" class="btn btn-warning" disabled>
                                <i class="fas fa-magic me-2"></i>Auto Process All
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>Instructions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <h6 class="text-primary">🚀 Quick Start:</h6>
                            <ol class="small mb-3">
                                <li><strong>Run the Chrome launcher:</strong><br>
                                    <button class="btn btn-sm btn-outline-primary me-2" onclick="window.open('launch_chrome_debug.bat')">
                                        <i class="fas fa-download me-1"></i>launch_chrome_debug.bat
                                    </button>
                                    <small class="text-muted">or</small>
                                    <button class="btn btn-sm btn-outline-info ms-2" onclick="window.open('launch_chrome_debug.ps1')">
                                        <i class="fas fa-download me-1"></i>launch_chrome_debug.ps1
                                    </button>
                                </li>
                                <li>Log into XactAnalysis in the Chrome window</li>
                                <li>Click "Connect to Browser" above</li>
                                <li>Use the processing buttons to extract claim notes</li>
                            </ol>
                        </div>

                        <div class="mb-3">
                            <h6 class="text-warning">⚠️ Manual Method:</h6>
                            <div class="small">
                                <p class="mb-2">If the launcher doesn't work, try manually:</p>
                                <ol class="mb-2">
                                    <li>Close all Chrome windows</li>
                                    <li>Open Command Prompt as Administrator</li>
                                    <li>Run: <code class="user-select-all">chrome.exe --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug"</code></li>
                                </ol>
                            </div>
                        </div>

                        <div>
                            <h6 class="text-info">🔧 Troubleshooting:</h6>
                            <div class="small">
                                <ul class="mb-0">
                                    <li><strong>Port already in use:</strong> Try port 9223 or 9224</li>
                                    <li><strong>Connection fails:</strong> Wait 10-15 seconds after starting Chrome</li>
                                    <li><strong>Permission issues:</strong> Run as Administrator</li>
                                    <li><strong>Firewall:</strong> Allow Chrome through Windows Firewall</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Status and Progress -->
            <div class="col-md-8">
                <!-- Progress Overview -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie me-2"></i>Progress Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <h3 id="total-claims" class="text-primary">{{ claims_summary.total or 0 }}</h3>
                                    <p class="text-muted">Total Claims</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <h3 id="completed-claims" class="text-success">{{ claims_summary.completed or 0 }}</h3>
                                    <p class="text-muted">Completed</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <h3 id="pending-claims" class="text-warning">{{ claims_summary.pending or 0 }}</h3>
                                    <p class="text-muted">Pending</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-card">
                                    <h3 id="progress-percent" class="text-info">{{ claims_summary.progress_percent or 0 }}%</h3>
                                    <p class="text-muted">Progress</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="progress">
                                <div id="progress-bar" class="progress-bar bg-success" role="progressbar" 
                                     style="width: {{ claims_summary.progress_percent or 0 }}%">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>Activity Log
                        </h5>
                        <button id="clear-log-btn" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-trash me-1"></i>Clear
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="activity-log" class="activity-log">
                            <div class="log-entry text-muted">
                                <small><i class="fas fa-info-circle me-1"></i>Ready to start processing...</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Processing -->
                <div id="current-processing" class="card" style="display: none;">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cog fa-spin me-2"></i>Currently Processing
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>Claim:</strong> <span id="current-claim">-</span><br>
                                <small class="text-muted">Step: <span id="current-step">-</span></small>
                            </div>
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
